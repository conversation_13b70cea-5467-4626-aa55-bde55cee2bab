const { getDatabase } = require('../../../config/database.config');
const { getModels } = require('../../../models');
const { logger } = require('../../../utils/logger');

class FavoriteController {
  constructor() {
    // No need to store database connection, we'll use getModels() directly
  }

  async addToFavorites(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;
      const models = getModels('write');

      // Check if already exists to prevent duplicates
      const existing = await models.Favorite.findOne({
        where: {
          UserID: userId,
          ListingID: id
        }
      });

      if (existing) {
        return reply.code(409).send({
          success: false,
          message: 'Listing already in favorites'
        });
      }

      const favorite = await models.Favorite.create({
        UserID: userId,
        ListingID: id
      });

      return reply.code(201).send({
        success: true,
        message: 'Listing added to favorites',
        data: favorite
      });
    } catch (error) {
      logger.error('Error adding to favorites:', error);
      
      // Handle unique constraint violation
      if (error.name === 'SequelizeUniqueConstraintError') {
        return reply.code(409).send({
          success: false,
          message: 'Listing already in favorites'
        });
      }

      return reply.code(500).send({
        success: false,
        message: 'Error adding to favorites',
        error: error.message
      });
    }
  }

  async removeFromFavorites(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;
      const models = getModels('write');

      const deletedCount = await models.Favorite.destroy({
        where: {
          UserID: userId,
          ListingID: id
        }
      });

      if (deletedCount === 0) {
        return reply.code(404).send({
          success: false,
          message: 'Favorite not found'
        });
      }

      return reply.code(200).send({
        success: true,
        message: 'Listing removed from favorites'
      });
    } catch (error) {
      logger.error('Error removing from favorites:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error removing from favorites',
        error: error.message
      });
    }
  }

  async getUserFavorites(request, reply) {
    try {
      const userId = request.user.id;
      const { page = 1, limit = 20 } = request.query;
      const offset = (page - 1) * parseInt(limit);
      const models = getModels('read');

      // Debug: Log the models to see what's available
      console.log('Available models:', Object.keys(models));
      console.log('User ID:', userId);

      // First, let's try to get favorites without associations to see if basic query works
      const simpleFavorites = await models.Favorite.findAll({
        where: { UserID: userId },
        attributes: ['ID', 'UserID', 'ListingID', 'CreatedAt']
      });
      console.log('Simple favorites (no associations):', simpleFavorites.length);
      if (simpleFavorites.length > 0) {
        console.log('First simple favorite:', simpleFavorites[0].toJSON());
      }

      // Method 1: Include the Listing model with explicit attributes
      const { count, rows: favorites } = await models.Favorite.findAndCountAll({
        where: { UserID: userId },
        include: [{
          model: models.Listing,
          as: 'Listing',
          required: false,
          attributes: [
            'ListingID', 'Title', 'Description', 'Location',
            'PricePerDay', 'IsAvailable', 'IsActive', 'CreatedAt'
          ]
        }],
        attributes: ['ID', 'UserID', 'ListingID', 'CreatedAt'],
        limit: parseInt(limit),
        offset: offset,
        order: [['CreatedAt', 'DESC']]
      });

      // Debug: Log the raw results
      console.log('Favorites count:', count);
      console.log('Favorites rows:', favorites.length);
      if (favorites.length > 0) {
        console.log('First favorite:', JSON.stringify(favorites[0], null, 2));
      }

      // If no data in favorites or associations aren't working, try raw query
      if (favorites.length === 0 || !favorites[0].Listing) {
        console.log('Trying raw query approach...');
        const { QueryTypes } = require('sequelize');
        const db = getDatabase('read');

        const rawFavorites = await db.query(`
          SELECT
            f."ID" as favorite_id,
            f."UserID" as user_id,
            f."ListingID" as listing_id,
            f."CreatedAt" as favorite_created_at,
            l."ListingID",
            l."Title",
            l."Description",
            l."Location",
            l."PricePerDay",
            l."IsAvailable",
            l."IsActive",
            l."CreatedAt" as listing_created_at
          FROM "favorites" f
          LEFT JOIN "listings" l ON f."ListingID" = l."ListingID"
          WHERE f."UserID" = :userId
          ORDER BY f."CreatedAt" DESC
          LIMIT :limit OFFSET :offset
        `, {
          replacements: {
            userId,
            limit: parseInt(limit),
            offset: offset
          },
          type: QueryTypes.SELECT
        });

        // Transform raw results to match expected format
        const transformedFavorites = rawFavorites.map(row => ({
          ID: row.favorite_id,
          UserID: row.user_id,
          ListingID: row.listing_id,
          CreatedAt: row.favorite_created_at,
          Listing: row.ListingID ? {
            ListingID: row.ListingID,
            Title: row.Title,
            Description: row.Description,
            Location: row.Location,
            PricePerDay: row.PricePerDay,
            IsAvailable: row.IsAvailable,
            IsActive: row.IsActive,
            CreatedAt: row.listing_created_at
          } : null
        }));

        console.log('Raw query results:', transformedFavorites.length);

        return reply.code(200).send({
          success: true,
          data: transformedFavorites,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            totalPages: Math.ceil(count / parseInt(limit))
          }
        });
      }

      return reply.code(200).send({
        success: true,
        data: favorites,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / parseInt(limit))
        }
      });
    } catch (error) {
      logger.error('Error getting user favorites:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error getting user favorites',
        error: error.message
      });
    }
  }

  async checkFavoriteStatus(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user.id;
      const models = getModels('read');

      const favorite = await models.Favorite.findOne({
        where: {
          UserID: userId,
          ListingID: id
        },
        attributes: ['ID'] // Only fetch the ID for better performance
      });

      return reply.code(200).send({
        success: true,
        data: {
          isFavorited: !!favorite,
          favoriteId: favorite ? favorite.ID : null
        }
      });
    } catch (error) {
      logger.error('Error checking favorite status:', error);
      return reply.code(500).send({
        success: false,
        message: 'Error checking favorite status',
        error: error.message
      });
    }
  }

  // Debug endpoint to test database and associations
  async debugFavorites(request, reply) {
    try {
      const userId = request.user.id;
      const models = getModels('read');

      console.log('=== DEBUG FAVORITES ===');
      console.log('User ID:', userId);
      console.log('Available models:', Object.keys(models));

      // Test 1: Check if we can find any favorites at all
      const allFavorites = await models.Favorite.findAll({
        where: { UserID: userId },
        raw: true
      });
      console.log('All favorites (raw):', allFavorites);

      // Test 2: Check if we can find any listings
      const allListings = await models.Listing.findAll({
        limit: 3,
        raw: true
      });
      console.log('Sample listings:', allListings);

      // Test 3: Try the association query
      let associationResult = null;
      try {
        associationResult = await models.Favorite.findAll({
          where: { UserID: userId },
          include: [{
            model: models.Listing,
            as: 'Listing',
            required: false
          }]
        });
        console.log('Association query result:', associationResult.length);
        if (associationResult.length > 0) {
          console.log('First association result:', JSON.stringify(associationResult[0], null, 2));
        }
      } catch (assocError) {
        console.log('Association error:', assocError.message);
      }

      return reply.code(200).send({
        success: true,
        debug: {
          userId,
          favoritesCount: allFavorites.length,
          listingsCount: allListings.length,
          associationWorking: !!associationResult,
          favorites: allFavorites,
          sampleListings: allListings.slice(0, 2),
          associationResult: associationResult ? associationResult.map(f => f.toJSON()) : null
        }
      });
    } catch (error) {
      console.error('Debug error:', error);
      return reply.code(500).send({
        success: false,
        error: error.message,
        stack: error.stack
      });
    }
  }
}

module.exports = FavoriteController;