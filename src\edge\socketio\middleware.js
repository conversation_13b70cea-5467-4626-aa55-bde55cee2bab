const { logger } = require('../../utils/logger');
const { metrics } = require('../../monitoring/prometheus')

const eventSchemas = {
  'order:create': 'orderCreate',
  'order:update': 'orderUpdate',
  'order:getAll': 'orderQuery',

  'user:update': 'userUpdate',

  'login': 'login',
  'register': 'userCreate'
}

const authMiddleware = (io, container) => {
  return async (socket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization;
      if (!token) {
        return next(new Error('Authentication required'));
      }

      // Remove 'Bearer ' prefix if present
      const cleanToken = token.replace('Bearer ', '');

      try {
        // Verify JWT token
        if (cleanToken == process.env.SPECIAL_SOCKET_TOKEN) {
          socket.userId = null;
          socket.userEmail = null;
          socket.userRole = null;
          return next();
        }

        const decoded = await io.jwt.verify(cleanToken);

        // Get user from database
        const userRepository = container.resolve('userRepository');
        const user = await userRepository.findById(decoded.id);
        // console.log(decoded+"densafhkwef");

        if (!user || user.status !== 'active') {
          return next(new Error('User not found or inactive'));
        }

        // Attach user info to socket
        socket.userId = user.id;
        socket.userEmail = user.email;
        socket.userRole = user.role;

        logger.debug('Socket authenticated', {
          socketId: socket.id,
          userId: user.id
        });

        next();
      } catch (error) {
        logger.error(`Socket auth error:, ${error}`);
        next(new Error('Invalid token'));
      }
    } catch (error) {
      logger.error(`Authentication failed ${error.message}`)
      next(new Error('Authentication failed'));
    }
  };
};

function defineSocketPerformanceMiddleware(container) {

  return function (socket, next) {

    // Store original emit and on methods
    const originalOn = socket.on;

    // Override on method to measure performance
    socket.on = function (event, handler) {
      originalOn.call(socket, event, async (...args) => {

        try {
          if (['disconnect'].includes(event)) return await handler;

          if (typeof args !== 'object') throw Error('Unexpected data in socket event')
          const parsedData = JSON.parse(args);

          let validator = parsedData;
          if (event.includes("order")) {
            validator = container.resolve("orderValidator");
          }

          if (['user', 'login', 'register'].some(keyword => event.includes(keyword))) {
            validator = container.resolve("userValidator");
          }

          // // Validate UUID
          if ((parsedData.userId !== undefined) && (!validator.isUUID(parsedData.userId))) {
            throw new Error('Invalid user ID format');
          }

          let validatedData = parsedData;
          const schemaKey = eventSchemas[event];

          if (schemaKey) {
            validatedData = await validator.validate(parsedData, schemaKey);
          }

          const start = performance.now();
          await handler(...args);
          const duration = performance.now() - start;

          metrics.recordSocketEvent(event, duration);

          // Log performance metrics
          console.log(`[Performance] Event received: ${event}, Time: ${duration.toFixed(3)}ms`);
        } catch (error) {
          logger.error(`Failed to entertain the event ${event}: ${error}`)
          socket.emit('error', `${error}`)
        }
      });
    };

    next();
  }
}

const rateLimitMiddleware = (maxRequests = 100, windowMs = 60000) => {
  const requests = new Map();

  return (socket, next) => {
    const now = Date.now();
    const userId = socket.userId || socket.id;

    if (!requests.has(userId)) {
      requests.set(userId, []);
    }

    const userRequests = requests.get(userId);
    const recentRequests = userRequests.filter(timestamp => now - timestamp < windowMs);

    if (recentRequests.length >= maxRequests) {
      return next(new Error('Rate limit exceeded'));
    }

    recentRequests.push(now);
    requests.set(userId, recentRequests);

    next();
  };
};

const validationMiddleware = (schema) => {
  return (socket, next) => {
    const { error } = schema.validate(socket.data);

    if (error) {
      return next(new Error(`Validation error: ${error.message}`));
    }

    next();
  };
};

module.exports = {
  authMiddleware,
  rateLimitMiddleware,
  validationMiddleware,
  defineSocketPerformanceMiddleware
};