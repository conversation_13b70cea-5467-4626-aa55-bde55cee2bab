const { logger } = require('../../utils/logger');

const eventSchemas = {
    'order:create': 'orderCreate',
    'order:update': 'orderUpdate',
    'order:getAll': 'orderQuery',

    'user:update': 'userUpdate',

    'login': 'login',
    'register': 'userCreate'
}

function defineValidationMiddleware(socket, container) {

    return async function ([eventName, data], next) {

        try {
            const parsedData = JSON.parse(data);
            let validator = parsedData;
            if (eventName.includes("order")) {
                validator = container.resolve("orderValidator");
            }

            if (['user', 'login', 'register'].some(keyword => eventName.includes(keyword))) {
                validator = container.resolve("userValidator");
            }

            // // Validate UUID
            if ((parsedData.userId !== undefined) && (!validator.isUUID(parsedData.userId))) {
                throw new Error('Invalid user ID format');
            }

            let validatedData = parsedData;
            const schemaKey = eventSchemas[eventName];

            if (schemaKey) {
                validatedData = await validator.validate(parsedData, schemaKey);
            }
            socket.context = {
                start: Date.now()
            }
            return next(null, [eventName, validatedData]);

        } catch (error) {
            logger.error(`Error while validating: ${error}`);
            socket.emit('error', { message: 'Data not valid' });
            return;
        }

    }
}

module.exports = defineValidationMiddleware;