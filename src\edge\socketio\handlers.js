const { logger } = require('../../utils/logger');
const { v4: uuidv4 } = require('uuid');
const { publishToRabbit } = require('../../config/queue.config');

const createHandlers = (io, container) => {
  const activeSubscriptions = new Map();

  return {
    async handleSubscribe(socket, data) {
      try {
        const { channel, filters = {} } = data;

        // Validate channel
        const allowedChannels = ['orders', 'notifications', 'users'];
        if (!allowedChannels.includes(channel)) {
          throw new Error('Invalid channel');
        }

        // Store subscription
        if (!activeSubscriptions.has(socket.id)) {
          activeSubscriptions.set(socket.id, new Set());
        }

        activeSubscriptions.get(socket.id).add(channel);

        // Join appropriate rooms based on channel and filters
        switch (channel) {
          case 'orders':
            socket.join(`orders:${socket.userId}`);
            if (filters.orderId) {
              socket.join(`order:${filters.orderId}`);
            }
            break;

          case 'notifications':
            socket.join(`notifications:${socket.userId}`);
            break;

          case 'users':
            // if (socket.userRole === 'admin' && filters.userId) {
            socket.join(`user:${filters.userId}`);
            // }
            break;
        }

        socket.emit('subscribed', { channel, filters });

        logger.debug(`Socket ${socket.id} subscribed to ${channel}`);
      } catch (error) {
        logger.error(`Subscribe error: ${error}`);
        socket.emit('error', { message: error.message });
      }
    },

    async handleUnsubscribe(socket, data) {
      try {
        const { channel } = data;

        if (activeSubscriptions.has(socket.id)) {
          activeSubscriptions.get(socket.id).delete(channel);
        }

        // Leave rooms
        const rooms = Array.from(socket.rooms);
        rooms.forEach(room => {
          if (room.startsWith(`${channel}:`)) {
            socket.leave(room);
          }
        });

        socket.emit('unsubscribed', { channel });

        logger.debug(`Socket ${socket.id} unsubscribed from ${channel}`);
      } catch (error) {
        logger.error(`Unsubscribe error: ${error}`);
        socket.emit('error', { message: error.message });
      }
    },
    async handleCreateUser(socket, data) {
      try {

        const parsedData = JSON.parse(data);
        // const validator = container.resolve('userValidator')
        // const validatedData = await validator.validate(JSON.parse(data), 'userCreate');
        const userMediator = container.resolve('userMediator');
        const result = await userMediator.createUser(parsedData);

        socket.emit('register', result)

      } catch (error) {
        logger.error(`Failed to create the user: ${error.message}`)
        socket.emit('error', { message: error.message });
      }
    },
    async handleUserLogin(socket, data) {
      try {
        // const { email, password } = data;      
        const parsedData = JSON.parse(data);
        // const userRepository = container.resolve('userRepository')
        // const userService = container.resolve('userService')
        const authMediator = container.resolve("authMediator");
        const result = await authMediator.login(parsedData, io.jwt);


        // Find user by email
        // const user = await userRepository.findByEmail(email);

        // if (!user) {
        //   throw new Error('Invalid credentials');
        // }

        // Check if account is locked
        // if (user.isLocked()) {
        //   throw new Error('Account is locked. Please try again later');
        // }

        // // Verify password
        // const isValidPassword = await user.comparePassword(password);

        // if (!isValidPassword) {
        //   // Increment login attempts
        //   await user.incrementLoginAttempts();
        //   throw new Error('Invalid credentials');
        // }

        // // Check if account is active
        // if (user.status !== 'active') {
        //   throw new Error('Account is not active');
        // }

        // Reset login attempts
        // await user.resetLoginAttempts();

        // Update last login
        // await userService.updateLastLogin(user.id);

        // Generate tokens
        // const accessToken = await io.jwt.sign({
        //   id: user.id,
        //   email: user.email,
        //   role: user.role
        // });

        // const refreshToken = uuidv4();

        // socket.emit('login', {
        //   success: true,
        //   user: user.toJSON(),
        //   accessToken,
        //   refreshToken,
        //   expiresIn: 1000 * 60 * 60 * 24 * 7
        // })

        socket.emit('login', result);

      } catch (error) {
        logger.error(`Failed to login: ${error.message}`)
        socket.emit('error', { message: error.message });
      }
    },
    async handleGetUser(socket, data) {
      try {
        const parsedData = JSON.parse(data);
        const { userId } = parsedData;
        // const validator = container.resolve('userValidator')
        // Validate UUID
        if (!userId) {
          throw new Error('Failed to get the user, user Id not found')
        }
        // if (!validator.isUUID(userId)) {
        //   throw new Error('Invalid user ID format');
        // }


        const userMediator = container.resolve('userMediator');
        const result = await userMediator.getUser(userId)
        socket.emit('user:get', result)

      } catch (error) {
        logger.error(`Failed to get the user: ${error}`);
        socket.emit('error', { message: error.message });
      }
    },

    async handleUpdateUser(socket, data) {
      try {
        const parsedData = JSON.parse(data);

        // const validator = container.resolve('userValidator');
        // const validatedData = await validator.validate(JSON.parse(data), 'userUpdate')

        const userMediator = container.resolve('userMediator')
        const result = await userMediator.updateUser(socket.userId, parsedData);

        socket.emit('user:update', result);

      } catch (error) {
        logger.error(`Failed to update the user: ${error}`);
        socket.emit('error', { message: error.message });
      }
    },
    async handleDeleteUser(socket, data) {
      try {

        const userMediator = container.resolve('userMediator')
        const result = await userMediator.deleteUser(socket.userId)
        socket.emit('user:delete', result)
        this.handleDisconnect(socket)
        socket.disconnect(true)

      } catch (error) {
        logger.error(`Failed to delete the user: ${error}`);
        socket.emit('error', { message: error.message });
      }
    },

    async handleUserStatus(socket, data) {
      try {
        const { status } = data;
        const validStatuses = ['online', 'away', 'busy', 'offline'];

        if (!validStatuses.includes(status)) {
          throw new Error('Invalid status');
        }

        // Update user status in cache
        const { cache } = require('../../config/cache.config');
        await cache.set(
          `user:status:${socket.userId}`,
          { status, lastSeen: new Date() },
          300 // 5 minutes
        );

        // Broadcast to user's contacts (implement contact system)
        socket.broadcast.emit('user:status-changed', {
          userId: socket.userId,
          status
        });

        logger.debug(`User ${socket.userId} status changed to ${status}`);
      } catch (error) {
        logger.error(`User status error: ${error}`);
        socket.emit('error', { message: error.message });
      }
    },

    async handleChatMessage(socket, data) {
      try {
        const { recipientId, message, type = 'text' } = data;

        // Validate message
        if (!message || message.trim().length === 0) {
          throw new Error('Message cannot be empty');
        }

        // Store message (implement message service)
        const messageData = {
          id: Date.now().toString(),
          senderId: socket.userId,
          recipientId,
          message,
          type,
          timestamp: new Date(),
          read: false
        };

        // Publish to queue for persistence
        await publishToRabbit('events', 'chat.message', messageData);

        return messageData;
      } catch (error) {
        logger.error(`Chat message error: ${error}`);
        throw error;
      }
    },

    async handleNotificationRead(socket, data) {
      try {
        const { notificationId } = data;

        // Update notification status (implement notification service)
        const { cache } = require('../../config/cache.config');
        await cache.set(
          `notification:read:${notificationId}`,
          { userId: socket.userId, readAt: new Date() },
          86400 // 24 hours
        );

        logger.debug(`Notification ${notificationId} marked as read`);
      } catch (error) {
        logger.error(`Notification read error: ${error}`);
        throw error;
      }
    },

    async handleMarkAllNotificationsRead(socket) {
      try {
        // Mark all notifications as read for user
        await publishToRabbit('events', 'notification.mark-all-read', {
          userId: socket.userId,
          timestamp: new Date()
        });

        logger.debug(`All notifications marked as read for user ${socket.userId}`);
      } catch (error) {
        logger.error(`Mark all notifications read error: ${error}`);
        throw error;
      }
    },

    handleDisconnect(socket) {
      // Clean up subscriptions
      activeSubscriptions.delete(socket.id);

      // Update user status to offline
      if (socket.userId) {
        const { cache } = require('../../config/cache.config');
        cache.set(
          `user:status:${socket.userId}`,
          { status: 'offline', lastSeen: new Date() },
          300
        ).catch(err => logger.error('Failed to update user status:', err));
      }
    }
  };
};

module.exports = { createHandlers };