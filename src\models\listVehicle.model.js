// models/vehicle.model.js
const { DataTypes } = require('sequelize');

const defineVehicleModel = (sequelize) => {
  const Vehicle = sequelize.define('Vehicle', {
    VehicleID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    ListingID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'listings',
        key: 'ListingID'
      }
    },
    VehicleType: { type: DataTypes.STRING, allowNull: false },
    BrandName: { type: DataTypes.STRING, allowNull: false },
    ModelName: { type: DataTypes.STRING, allowNull: false },
    RegistrationNumber: { type: DataTypes.STRING, allowNull: true },
    YearOfManufacture: { type: DataTypes.INTEGER, allowNull: true },
    Color: { type: DataTypes.STRING, allowNull: true },
    NumberOfSeats: { type: DataTypes.INTEGER, allowNull: true },
    AvailabilityFrom: { type: DataTypes.DATE, allowNull: true },
    AvailabilityTo: { type: DataTypes.DATE, allowNull: true },
    PickupLocation: { type: DataTypes.STRING, allowNull: true }
  }, {
    tableName: 'vehicles',
    timestamps: false
  });

  Vehicle.associate = (models) => {
    Vehicle.belongsTo(models.Listing, {
      foreignKey: 'ListingID',
      as: 'Listing'
    });
  };

  return Vehicle;
};

module.exports = defineVehicleModel;
